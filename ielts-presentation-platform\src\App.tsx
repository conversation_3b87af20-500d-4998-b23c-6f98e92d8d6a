import React from 'react'
import { Routes, Route } from 'react-router-dom'
import MainMenu from './components/MainMenu/MainMenu'
import LessonViewer from './components/LessonViewer/LessonViewer'

function App() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-pioneer-blue to-pioneer-green">
      <Routes>
        <Route path="/" element={<MainMenu />} />
        <Route path="/students-book" element={<LessonViewer section="students-book" />} />
        <Route path="/workbook" element={<LessonViewer section="workbook" />} />
        <Route path="/vocabulary" element={<LessonViewer section="vocabulary" />} />
        <Route path="/videos-worksheets" element={<LessonViewer section="videos-worksheets" />} />
        <Route path="/tests" element={<LessonViewer section="tests" />} />
      </Routes>
    </div>
  )
}

export default App
