// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const Fa500Px: IconType;
export declare const FaAccessibleIcon: IconType;
export declare const FaAccusoft: IconType;
export declare const FaAcquisitionsIncorporated: IconType;
export declare const FaAdn: IconType;
export declare const FaAdversal: IconType;
export declare const FaAffiliatetheme: IconType;
export declare const FaAirbnb: IconType;
export declare const FaAlgolia: IconType;
export declare const FaAlipay: IconType;
export declare const FaAmazonPay: IconType;
export declare const FaAmazon: IconType;
export declare const FaAmilia: IconType;
export declare const FaAndroid: IconType;
export declare const FaAngellist: IconType;
export declare const FaAngrycreative: IconType;
export declare const FaAngular: IconType;
export declare const FaAppStoreIos: IconType;
export declare const FaAppStore: IconType;
export declare const FaApper: IconType;
export declare const FaApplePay: IconType;
export declare const FaApple: IconType;
export declare const FaArtstation: IconType;
export declare const FaAsymmetrik: IconType;
export declare const FaAtlassian: IconType;
export declare const FaAudible: IconType;
export declare const FaAutoprefixer: IconType;
export declare const FaAvianex: IconType;
export declare const FaAviato: IconType;
export declare const FaAws: IconType;
export declare const FaBandcamp: IconType;
export declare const FaBattleNet: IconType;
export declare const FaBehanceSquare: IconType;
export declare const FaBehance: IconType;
export declare const FaBimobject: IconType;
export declare const FaBitbucket: IconType;
export declare const FaBitcoin: IconType;
export declare const FaBity: IconType;
export declare const FaBlackTie: IconType;
export declare const FaBlackberry: IconType;
export declare const FaBloggerB: IconType;
export declare const FaBlogger: IconType;
export declare const FaBluetoothB: IconType;
export declare const FaBluetooth: IconType;
export declare const FaBootstrap: IconType;
export declare const FaBtc: IconType;
export declare const FaBuffer: IconType;
export declare const FaBuromobelexperte: IconType;
export declare const FaBuyNLarge: IconType;
export declare const FaBuysellads: IconType;
export declare const FaCanadianMapleLeaf: IconType;
export declare const FaCcAmazonPay: IconType;
export declare const FaCcAmex: IconType;
export declare const FaCcApplePay: IconType;
export declare const FaCcDinersClub: IconType;
export declare const FaCcDiscover: IconType;
export declare const FaCcJcb: IconType;
export declare const FaCcMastercard: IconType;
export declare const FaCcPaypal: IconType;
export declare const FaCcStripe: IconType;
export declare const FaCcVisa: IconType;
export declare const FaCentercode: IconType;
export declare const FaCentos: IconType;
export declare const FaChrome: IconType;
export declare const FaChromecast: IconType;
export declare const FaCloudflare: IconType;
export declare const FaCloudscale: IconType;
export declare const FaCloudsmith: IconType;
export declare const FaCloudversify: IconType;
export declare const FaCodepen: IconType;
export declare const FaCodiepie: IconType;
export declare const FaConfluence: IconType;
export declare const FaConnectdevelop: IconType;
export declare const FaContao: IconType;
export declare const FaCottonBureau: IconType;
export declare const FaCpanel: IconType;
export declare const FaCreativeCommonsBy: IconType;
export declare const FaCreativeCommonsNcEu: IconType;
export declare const FaCreativeCommonsNcJp: IconType;
export declare const FaCreativeCommonsNc: IconType;
export declare const FaCreativeCommonsNd: IconType;
export declare const FaCreativeCommonsPdAlt: IconType;
export declare const FaCreativeCommonsPd: IconType;
export declare const FaCreativeCommonsRemix: IconType;
export declare const FaCreativeCommonsSa: IconType;
export declare const FaCreativeCommonsSamplingPlus: IconType;
export declare const FaCreativeCommonsSampling: IconType;
export declare const FaCreativeCommonsShare: IconType;
export declare const FaCreativeCommonsZero: IconType;
export declare const FaCreativeCommons: IconType;
export declare const FaCriticalRole: IconType;
export declare const FaCss3Alt: IconType;
export declare const FaCss3: IconType;
export declare const FaCuttlefish: IconType;
export declare const FaDAndDBeyond: IconType;
export declare const FaDAndD: IconType;
export declare const FaDailymotion: IconType;
export declare const FaDashcube: IconType;
export declare const FaDeezer: IconType;
export declare const FaDelicious: IconType;
export declare const FaDeploydog: IconType;
export declare const FaDeskpro: IconType;
export declare const FaDev: IconType;
export declare const FaDeviantart: IconType;
export declare const FaDhl: IconType;
export declare const FaDiaspora: IconType;
export declare const FaDigg: IconType;
export declare const FaDigitalOcean: IconType;
export declare const FaDiscord: IconType;
export declare const FaDiscourse: IconType;
export declare const FaDochub: IconType;
export declare const FaDocker: IconType;
export declare const FaDraft2Digital: IconType;
export declare const FaDribbbleSquare: IconType;
export declare const FaDribbble: IconType;
export declare const FaDropbox: IconType;
export declare const FaDrupal: IconType;
export declare const FaDyalog: IconType;
export declare const FaEarlybirds: IconType;
export declare const FaEbay: IconType;
export declare const FaEdgeLegacy: IconType;
export declare const FaEdge: IconType;
export declare const FaElementor: IconType;
export declare const FaEllo: IconType;
export declare const FaEmber: IconType;
export declare const FaEmpire: IconType;
export declare const FaEnvira: IconType;
export declare const FaErlang: IconType;
export declare const FaEthereum: IconType;
export declare const FaEtsy: IconType;
export declare const FaEvernote: IconType;
export declare const FaExpeditedssl: IconType;
export declare const FaFacebookF: IconType;
export declare const FaFacebookMessenger: IconType;
export declare const FaFacebookSquare: IconType;
export declare const FaFacebook: IconType;
export declare const FaFantasyFlightGames: IconType;
export declare const FaFedex: IconType;
export declare const FaFedora: IconType;
export declare const FaFigma: IconType;
export declare const FaFirefoxBrowser: IconType;
export declare const FaFirefox: IconType;
export declare const FaFirstOrderAlt: IconType;
export declare const FaFirstOrder: IconType;
export declare const FaFirstdraft: IconType;
export declare const FaFlickr: IconType;
export declare const FaFlipboard: IconType;
export declare const FaFly: IconType;
export declare const FaFontAwesomeAlt: IconType;
export declare const FaFontAwesomeFlag: IconType;
export declare const FaFontAwesomeLogoFull: IconType;
export declare const FaFontAwesome: IconType;
export declare const FaFonticonsFi: IconType;
export declare const FaFonticons: IconType;
export declare const FaFortAwesomeAlt: IconType;
export declare const FaFortAwesome: IconType;
export declare const FaForumbee: IconType;
export declare const FaFoursquare: IconType;
export declare const FaFreeCodeCamp: IconType;
export declare const FaFreebsd: IconType;
export declare const FaFulcrum: IconType;
export declare const FaGalacticRepublic: IconType;
export declare const FaGalacticSenate: IconType;
export declare const FaGetPocket: IconType;
export declare const FaGgCircle: IconType;
export declare const FaGg: IconType;
export declare const FaGitAlt: IconType;
export declare const FaGitSquare: IconType;
export declare const FaGit: IconType;
export declare const FaGithubAlt: IconType;
export declare const FaGithubSquare: IconType;
export declare const FaGithub: IconType;
export declare const FaGitkraken: IconType;
export declare const FaGitlab: IconType;
export declare const FaGitter: IconType;
export declare const FaGlideG: IconType;
export declare const FaGlide: IconType;
export declare const FaGofore: IconType;
export declare const FaGoodreadsG: IconType;
export declare const FaGoodreads: IconType;
export declare const FaGoogleDrive: IconType;
export declare const FaGooglePay: IconType;
export declare const FaGooglePlay: IconType;
export declare const FaGooglePlusG: IconType;
export declare const FaGooglePlusSquare: IconType;
export declare const FaGooglePlus: IconType;
export declare const FaGoogleWallet: IconType;
export declare const FaGoogle: IconType;
export declare const FaGratipay: IconType;
export declare const FaGrav: IconType;
export declare const FaGripfire: IconType;
export declare const FaGrunt: IconType;
export declare const FaGuilded: IconType;
export declare const FaGulp: IconType;
export declare const FaHackerNewsSquare: IconType;
export declare const FaHackerNews: IconType;
export declare const FaHackerrank: IconType;
export declare const FaHips: IconType;
export declare const FaHireAHelper: IconType;
export declare const FaHive: IconType;
export declare const FaHooli: IconType;
export declare const FaHornbill: IconType;
export declare const FaHotjar: IconType;
export declare const FaHouzz: IconType;
export declare const FaHtml5: IconType;
export declare const FaHubspot: IconType;
export declare const FaIdeal: IconType;
export declare const FaImdb: IconType;
export declare const FaInnosoft: IconType;
export declare const FaInstagramSquare: IconType;
export declare const FaInstagram: IconType;
export declare const FaInstalod: IconType;
export declare const FaIntercom: IconType;
export declare const FaInternetExplorer: IconType;
export declare const FaInvision: IconType;
export declare const FaIoxhost: IconType;
export declare const FaItchIo: IconType;
export declare const FaItunesNote: IconType;
export declare const FaItunes: IconType;
export declare const FaJava: IconType;
export declare const FaJediOrder: IconType;
export declare const FaJenkins: IconType;
export declare const FaJira: IconType;
export declare const FaJoget: IconType;
export declare const FaJoomla: IconType;
export declare const FaJsSquare: IconType;
export declare const FaJs: IconType;
export declare const FaJsfiddle: IconType;
export declare const FaKaggle: IconType;
export declare const FaKeybase: IconType;
export declare const FaKeycdn: IconType;
export declare const FaKickstarterK: IconType;
export declare const FaKickstarter: IconType;
export declare const FaKorvue: IconType;
export declare const FaLaravel: IconType;
export declare const FaLastfmSquare: IconType;
export declare const FaLastfm: IconType;
export declare const FaLeanpub: IconType;
export declare const FaLess: IconType;
export declare const FaLine: IconType;
export declare const FaLinkedinIn: IconType;
export declare const FaLinkedin: IconType;
export declare const FaLinode: IconType;
export declare const FaLinux: IconType;
export declare const FaLyft: IconType;
export declare const FaMagento: IconType;
export declare const FaMailchimp: IconType;
export declare const FaMandalorian: IconType;
export declare const FaMarkdown: IconType;
export declare const FaMastodon: IconType;
export declare const FaMaxcdn: IconType;
export declare const FaMdb: IconType;
export declare const FaMedapps: IconType;
export declare const FaMediumM: IconType;
export declare const FaMedium: IconType;
export declare const FaMedrt: IconType;
export declare const FaMeetup: IconType;
export declare const FaMegaport: IconType;
export declare const FaMendeley: IconType;
export declare const FaMicroblog: IconType;
export declare const FaMicrosoft: IconType;
export declare const FaMix: IconType;
export declare const FaMixcloud: IconType;
export declare const FaMixer: IconType;
export declare const FaMizuni: IconType;
export declare const FaModx: IconType;
export declare const FaMonero: IconType;
export declare const FaNapster: IconType;
export declare const FaNeos: IconType;
export declare const FaNimblr: IconType;
export declare const FaNodeJs: IconType;
export declare const FaNode: IconType;
export declare const FaNpm: IconType;
export declare const FaNs8: IconType;
export declare const FaNutritionix: IconType;
export declare const FaOctopusDeploy: IconType;
export declare const FaOdnoklassnikiSquare: IconType;
export declare const FaOdnoklassniki: IconType;
export declare const FaOldRepublic: IconType;
export declare const FaOpencart: IconType;
export declare const FaOpenid: IconType;
export declare const FaOpera: IconType;
export declare const FaOptinMonster: IconType;
export declare const FaOrcid: IconType;
export declare const FaOsi: IconType;
export declare const FaPage4: IconType;
export declare const FaPagelines: IconType;
export declare const FaPalfed: IconType;
export declare const FaPatreon: IconType;
export declare const FaPaypal: IconType;
export declare const FaPennyArcade: IconType;
export declare const FaPerbyte: IconType;
export declare const FaPeriscope: IconType;
export declare const FaPhabricator: IconType;
export declare const FaPhoenixFramework: IconType;
export declare const FaPhoenixSquadron: IconType;
export declare const FaPhp: IconType;
export declare const FaPiedPiperAlt: IconType;
export declare const FaPiedPiperHat: IconType;
export declare const FaPiedPiperPp: IconType;
export declare const FaPiedPiperSquare: IconType;
export declare const FaPiedPiper: IconType;
export declare const FaPinterestP: IconType;
export declare const FaPinterestSquare: IconType;
export declare const FaPinterest: IconType;
export declare const FaPlaystation: IconType;
export declare const FaProductHunt: IconType;
export declare const FaPushed: IconType;
export declare const FaPython: IconType;
export declare const FaQq: IconType;
export declare const FaQuinscape: IconType;
export declare const FaQuora: IconType;
export declare const FaRProject: IconType;
export declare const FaRaspberryPi: IconType;
export declare const FaRavelry: IconType;
export declare const FaReact: IconType;
export declare const FaReacteurope: IconType;
export declare const FaReadme: IconType;
export declare const FaRebel: IconType;
export declare const FaRedRiver: IconType;
export declare const FaRedditAlien: IconType;
export declare const FaRedditSquare: IconType;
export declare const FaReddit: IconType;
export declare const FaRedhat: IconType;
export declare const FaRenren: IconType;
export declare const FaReplyd: IconType;
export declare const FaResearchgate: IconType;
export declare const FaResolving: IconType;
export declare const FaRev: IconType;
export declare const FaRocketchat: IconType;
export declare const FaRockrms: IconType;
export declare const FaRust: IconType;
export declare const FaSafari: IconType;
export declare const FaSalesforce: IconType;
export declare const FaSass: IconType;
export declare const FaSchlix: IconType;
export declare const FaScribd: IconType;
export declare const FaSearchengin: IconType;
export declare const FaSellcast: IconType;
export declare const FaSellsy: IconType;
export declare const FaServicestack: IconType;
export declare const FaShirtsinbulk: IconType;
export declare const FaShopify: IconType;
export declare const FaShopware: IconType;
export declare const FaSimplybuilt: IconType;
export declare const FaSistrix: IconType;
export declare const FaSith: IconType;
export declare const FaSketch: IconType;
export declare const FaSkyatlas: IconType;
export declare const FaSkype: IconType;
export declare const FaSlackHash: IconType;
export declare const FaSlack: IconType;
export declare const FaSlideshare: IconType;
export declare const FaSnapchatGhost: IconType;
export declare const FaSnapchatSquare: IconType;
export declare const FaSnapchat: IconType;
export declare const FaSoundcloud: IconType;
export declare const FaSourcetree: IconType;
export declare const FaSpeakap: IconType;
export declare const FaSpeakerDeck: IconType;
export declare const FaSpotify: IconType;
export declare const FaSquarespace: IconType;
export declare const FaStackExchange: IconType;
export declare const FaStackOverflow: IconType;
export declare const FaStackpath: IconType;
export declare const FaStaylinked: IconType;
export declare const FaSteamSquare: IconType;
export declare const FaSteamSymbol: IconType;
export declare const FaSteam: IconType;
export declare const FaStickerMule: IconType;
export declare const FaStrava: IconType;
export declare const FaStripeS: IconType;
export declare const FaStripe: IconType;
export declare const FaStudiovinari: IconType;
export declare const FaStumbleuponCircle: IconType;
export declare const FaStumbleupon: IconType;
export declare const FaSuperpowers: IconType;
export declare const FaSupple: IconType;
export declare const FaSuse: IconType;
export declare const FaSwift: IconType;
export declare const FaSymfony: IconType;
export declare const FaTeamspeak: IconType;
export declare const FaTelegramPlane: IconType;
export declare const FaTelegram: IconType;
export declare const FaTencentWeibo: IconType;
export declare const FaTheRedYeti: IconType;
export declare const FaThemeco: IconType;
export declare const FaThemeisle: IconType;
export declare const FaThinkPeaks: IconType;
export declare const FaTiktok: IconType;
export declare const FaTradeFederation: IconType;
export declare const FaTrello: IconType;
export declare const FaTripadvisor: IconType;
export declare const FaTumblrSquare: IconType;
export declare const FaTumblr: IconType;
export declare const FaTwitch: IconType;
export declare const FaTwitterSquare: IconType;
export declare const FaTwitter: IconType;
export declare const FaTypo3: IconType;
export declare const FaUber: IconType;
export declare const FaUbuntu: IconType;
export declare const FaUikit: IconType;
export declare const FaUmbraco: IconType;
export declare const FaUncharted: IconType;
export declare const FaUniregistry: IconType;
export declare const FaUnity: IconType;
export declare const FaUnsplash: IconType;
export declare const FaUntappd: IconType;
export declare const FaUps: IconType;
export declare const FaUsb: IconType;
export declare const FaUsps: IconType;
export declare const FaUssunnah: IconType;
export declare const FaVaadin: IconType;
export declare const FaViacoin: IconType;
export declare const FaViadeoSquare: IconType;
export declare const FaViadeo: IconType;
export declare const FaViber: IconType;
export declare const FaVimeoSquare: IconType;
export declare const FaVimeoV: IconType;
export declare const FaVimeo: IconType;
export declare const FaVine: IconType;
export declare const FaVk: IconType;
export declare const FaVnv: IconType;
export declare const FaVuejs: IconType;
export declare const FaWatchmanMonitoring: IconType;
export declare const FaWaze: IconType;
export declare const FaWeebly: IconType;
export declare const FaWeibo: IconType;
export declare const FaWeixin: IconType;
export declare const FaWhatsappSquare: IconType;
export declare const FaWhatsapp: IconType;
export declare const FaWhmcs: IconType;
export declare const FaWikipediaW: IconType;
export declare const FaWindows: IconType;
export declare const FaWix: IconType;
export declare const FaWizardsOfTheCoast: IconType;
export declare const FaWodu: IconType;
export declare const FaWolfPackBattalion: IconType;
export declare const FaWordpressSimple: IconType;
export declare const FaWordpress: IconType;
export declare const FaWpbeginner: IconType;
export declare const FaWpexplorer: IconType;
export declare const FaWpforms: IconType;
export declare const FaWpressr: IconType;
export declare const FaXbox: IconType;
export declare const FaXingSquare: IconType;
export declare const FaXing: IconType;
export declare const FaYCombinator: IconType;
export declare const FaYahoo: IconType;
export declare const FaYammer: IconType;
export declare const FaYandexInternational: IconType;
export declare const FaYandex: IconType;
export declare const FaYarn: IconType;
export declare const FaYelp: IconType;
export declare const FaYoast: IconType;
export declare const FaYoutubeSquare: IconType;
export declare const FaYoutube: IconType;
export declare const FaZhihu: IconType;
export declare const FaAd: IconType;
export declare const FaAddressBook: IconType;
export declare const FaAddressCard: IconType;
export declare const FaAdjust: IconType;
export declare const FaAirFreshener: IconType;
export declare const FaAlignCenter: IconType;
export declare const FaAlignJustify: IconType;
export declare const FaAlignLeft: IconType;
export declare const FaAlignRight: IconType;
export declare const FaAllergies: IconType;
export declare const FaAmbulance: IconType;
export declare const FaAmericanSignLanguageInterpreting: IconType;
export declare const FaAnchor: IconType;
export declare const FaAngleDoubleDown: IconType;
export declare const FaAngleDoubleLeft: IconType;
export declare const FaAngleDoubleRight: IconType;
export declare const FaAngleDoubleUp: IconType;
export declare const FaAngleDown: IconType;
export declare const FaAngleLeft: IconType;
export declare const FaAngleRight: IconType;
export declare const FaAngleUp: IconType;
export declare const FaAngry: IconType;
export declare const FaAnkh: IconType;
export declare const FaAppleAlt: IconType;
export declare const FaArchive: IconType;
export declare const FaArchway: IconType;
export declare const FaArrowAltCircleDown: IconType;
export declare const FaArrowAltCircleLeft: IconType;
export declare const FaArrowAltCircleRight: IconType;
export declare const FaArrowAltCircleUp: IconType;
export declare const FaArrowCircleDown: IconType;
export declare const FaArrowCircleLeft: IconType;
export declare const FaArrowCircleRight: IconType;
export declare const FaArrowCircleUp: IconType;
export declare const FaArrowDown: IconType;
export declare const FaArrowLeft: IconType;
export declare const FaArrowRight: IconType;
export declare const FaArrowUp: IconType;
export declare const FaArrowsAltH: IconType;
export declare const FaArrowsAltV: IconType;
export declare const FaArrowsAlt: IconType;
export declare const FaAssistiveListeningSystems: IconType;
export declare const FaAsterisk: IconType;
export declare const FaAt: IconType;
export declare const FaAtlas: IconType;
export declare const FaAtom: IconType;
export declare const FaAudioDescription: IconType;
export declare const FaAward: IconType;
export declare const FaBabyCarriage: IconType;
export declare const FaBaby: IconType;
export declare const FaBackspace: IconType;
export declare const FaBackward: IconType;
export declare const FaBacon: IconType;
export declare const FaBacteria: IconType;
export declare const FaBacterium: IconType;
export declare const FaBahai: IconType;
export declare const FaBalanceScaleLeft: IconType;
export declare const FaBalanceScaleRight: IconType;
export declare const FaBalanceScale: IconType;
export declare const FaBan: IconType;
export declare const FaBandAid: IconType;
export declare const FaBarcode: IconType;
export declare const FaBars: IconType;
export declare const FaBaseballBall: IconType;
export declare const FaBasketballBall: IconType;
export declare const FaBath: IconType;
export declare const FaBatteryEmpty: IconType;
export declare const FaBatteryFull: IconType;
export declare const FaBatteryHalf: IconType;
export declare const FaBatteryQuarter: IconType;
export declare const FaBatteryThreeQuarters: IconType;
export declare const FaBed: IconType;
export declare const FaBeer: IconType;
export declare const FaBellSlash: IconType;
export declare const FaBell: IconType;
export declare const FaBezierCurve: IconType;
export declare const FaBible: IconType;
export declare const FaBicycle: IconType;
export declare const FaBiking: IconType;
export declare const FaBinoculars: IconType;
export declare const FaBiohazard: IconType;
export declare const FaBirthdayCake: IconType;
export declare const FaBlenderPhone: IconType;
export declare const FaBlender: IconType;
export declare const FaBlind: IconType;
export declare const FaBlog: IconType;
export declare const FaBold: IconType;
export declare const FaBolt: IconType;
export declare const FaBomb: IconType;
export declare const FaBone: IconType;
export declare const FaBong: IconType;
export declare const FaBookDead: IconType;
export declare const FaBookMedical: IconType;
export declare const FaBookOpen: IconType;
export declare const FaBookReader: IconType;
export declare const FaBook: IconType;
export declare const FaBookmark: IconType;
export declare const FaBorderAll: IconType;
export declare const FaBorderNone: IconType;
export declare const FaBorderStyle: IconType;
export declare const FaBowlingBall: IconType;
export declare const FaBoxOpen: IconType;
export declare const FaBoxTissue: IconType;
export declare const FaBox: IconType;
export declare const FaBoxes: IconType;
export declare const FaBraille: IconType;
export declare const FaBrain: IconType;
export declare const FaBreadSlice: IconType;
export declare const FaBriefcaseMedical: IconType;
export declare const FaBriefcase: IconType;
export declare const FaBroadcastTower: IconType;
export declare const FaBroom: IconType;
export declare const FaBrush: IconType;
export declare const FaBug: IconType;
export declare const FaBuilding: IconType;
export declare const FaBullhorn: IconType;
export declare const FaBullseye: IconType;
export declare const FaBurn: IconType;
export declare const FaBusAlt: IconType;
export declare const FaBus: IconType;
export declare const FaBusinessTime: IconType;
export declare const FaCalculator: IconType;
export declare const FaCalendarAlt: IconType;
export declare const FaCalendarCheck: IconType;
export declare const FaCalendarDay: IconType;
export declare const FaCalendarMinus: IconType;
export declare const FaCalendarPlus: IconType;
export declare const FaCalendarTimes: IconType;
export declare const FaCalendarWeek: IconType;
export declare const FaCalendar: IconType;
export declare const FaCameraRetro: IconType;
export declare const FaCamera: IconType;
export declare const FaCampground: IconType;
export declare const FaCandyCane: IconType;
export declare const FaCannabis: IconType;
export declare const FaCapsules: IconType;
export declare const FaCarAlt: IconType;
export declare const FaCarBattery: IconType;
export declare const FaCarCrash: IconType;
export declare const FaCarSide: IconType;
export declare const FaCar: IconType;
export declare const FaCaravan: IconType;
export declare const FaCaretDown: IconType;
export declare const FaCaretLeft: IconType;
export declare const FaCaretRight: IconType;
export declare const FaCaretSquareDown: IconType;
export declare const FaCaretSquareLeft: IconType;
export declare const FaCaretSquareRight: IconType;
export declare const FaCaretSquareUp: IconType;
export declare const FaCaretUp: IconType;
export declare const FaCarrot: IconType;
export declare const FaCartArrowDown: IconType;
export declare const FaCartPlus: IconType;
export declare const FaCashRegister: IconType;
export declare const FaCat: IconType;
export declare const FaCertificate: IconType;
export declare const FaChair: IconType;
export declare const FaChalkboardTeacher: IconType;
export declare const FaChalkboard: IconType;
export declare const FaChargingStation: IconType;
export declare const FaChartArea: IconType;
export declare const FaChartBar: IconType;
export declare const FaChartLine: IconType;
export declare const FaChartPie: IconType;
export declare const FaCheckCircle: IconType;
export declare const FaCheckDouble: IconType;
export declare const FaCheckSquare: IconType;
export declare const FaCheck: IconType;
export declare const FaCheese: IconType;
export declare const FaChessBishop: IconType;
export declare const FaChessBoard: IconType;
export declare const FaChessKing: IconType;
export declare const FaChessKnight: IconType;
export declare const FaChessPawn: IconType;
export declare const FaChessQueen: IconType;
export declare const FaChessRook: IconType;
export declare const FaChess: IconType;
export declare const FaChevronCircleDown: IconType;
export declare const FaChevronCircleLeft: IconType;
export declare const FaChevronCircleRight: IconType;
export declare const FaChevronCircleUp: IconType;
export declare const FaChevronDown: IconType;
export declare const FaChevronLeft: IconType;
export declare const FaChevronRight: IconType;
export declare const FaChevronUp: IconType;
export declare const FaChild: IconType;
export declare const FaChurch: IconType;
export declare const FaCircleNotch: IconType;
export declare const FaCircle: IconType;
export declare const FaCity: IconType;
export declare const FaClinicMedical: IconType;
export declare const FaClipboardCheck: IconType;
export declare const FaClipboardList: IconType;
export declare const FaClipboard: IconType;
export declare const FaClock: IconType;
export declare const FaClone: IconType;
export declare const FaClosedCaptioning: IconType;
export declare const FaCloudDownloadAlt: IconType;
export declare const FaCloudMeatball: IconType;
export declare const FaCloudMoonRain: IconType;
export declare const FaCloudMoon: IconType;
export declare const FaCloudRain: IconType;
export declare const FaCloudShowersHeavy: IconType;
export declare const FaCloudSunRain: IconType;
export declare const FaCloudSun: IconType;
export declare const FaCloudUploadAlt: IconType;
export declare const FaCloud: IconType;
export declare const FaCocktail: IconType;
export declare const FaCodeBranch: IconType;
export declare const FaCode: IconType;
export declare const FaCoffee: IconType;
export declare const FaCog: IconType;
export declare const FaCogs: IconType;
export declare const FaCoins: IconType;
export declare const FaColumns: IconType;
export declare const FaCommentAlt: IconType;
export declare const FaCommentDollar: IconType;
export declare const FaCommentDots: IconType;
export declare const FaCommentMedical: IconType;
export declare const FaCommentSlash: IconType;
export declare const FaComment: IconType;
export declare const FaCommentsDollar: IconType;
export declare const FaComments: IconType;
export declare const FaCompactDisc: IconType;
export declare const FaCompass: IconType;
export declare const FaCompressAlt: IconType;
export declare const FaCompressArrowsAlt: IconType;
export declare const FaCompress: IconType;
export declare const FaConciergeBell: IconType;
export declare const FaCookieBite: IconType;
export declare const FaCookie: IconType;
export declare const FaCopy: IconType;
export declare const FaCopyright: IconType;
export declare const FaCouch: IconType;
export declare const FaCreditCard: IconType;
export declare const FaCropAlt: IconType;
export declare const FaCrop: IconType;
export declare const FaCross: IconType;
export declare const FaCrosshairs: IconType;
export declare const FaCrow: IconType;
export declare const FaCrown: IconType;
export declare const FaCrutch: IconType;
export declare const FaCube: IconType;
export declare const FaCubes: IconType;
export declare const FaCut: IconType;
export declare const FaDatabase: IconType;
export declare const FaDeaf: IconType;
export declare const FaDemocrat: IconType;
export declare const FaDesktop: IconType;
export declare const FaDharmachakra: IconType;
export declare const FaDiagnoses: IconType;
export declare const FaDiceD20: IconType;
export declare const FaDiceD6: IconType;
export declare const FaDiceFive: IconType;
export declare const FaDiceFour: IconType;
export declare const FaDiceOne: IconType;
export declare const FaDiceSix: IconType;
export declare const FaDiceThree: IconType;
export declare const FaDiceTwo: IconType;
export declare const FaDice: IconType;
export declare const FaDigitalTachograph: IconType;
export declare const FaDirections: IconType;
export declare const FaDisease: IconType;
export declare const FaDivide: IconType;
export declare const FaDizzy: IconType;
export declare const FaDna: IconType;
export declare const FaDog: IconType;
export declare const FaDollarSign: IconType;
export declare const FaDollyFlatbed: IconType;
export declare const FaDolly: IconType;
export declare const FaDonate: IconType;
export declare const FaDoorClosed: IconType;
export declare const FaDoorOpen: IconType;
export declare const FaDotCircle: IconType;
export declare const FaDove: IconType;
export declare const FaDownload: IconType;
export declare const FaDraftingCompass: IconType;
export declare const FaDragon: IconType;
export declare const FaDrawPolygon: IconType;
export declare const FaDrumSteelpan: IconType;
export declare const FaDrum: IconType;
export declare const FaDrumstickBite: IconType;
export declare const FaDumbbell: IconType;
export declare const FaDumpsterFire: IconType;
export declare const FaDumpster: IconType;
export declare const FaDungeon: IconType;
export declare const FaEdit: IconType;
export declare const FaEgg: IconType;
export declare const FaEject: IconType;
export declare const FaEllipsisH: IconType;
export declare const FaEllipsisV: IconType;
export declare const FaEnvelopeOpenText: IconType;
export declare const FaEnvelopeOpen: IconType;
export declare const FaEnvelopeSquare: IconType;
export declare const FaEnvelope: IconType;
export declare const FaEquals: IconType;
export declare const FaEraser: IconType;
export declare const FaEthernet: IconType;
export declare const FaEuroSign: IconType;
export declare const FaExchangeAlt: IconType;
export declare const FaExclamationCircle: IconType;
export declare const FaExclamationTriangle: IconType;
export declare const FaExclamation: IconType;
export declare const FaExpandAlt: IconType;
export declare const FaExpandArrowsAlt: IconType;
export declare const FaExpand: IconType;
export declare const FaExternalLinkAlt: IconType;
export declare const FaExternalLinkSquareAlt: IconType;
export declare const FaEyeDropper: IconType;
export declare const FaEyeSlash: IconType;
export declare const FaEye: IconType;
export declare const FaFan: IconType;
export declare const FaFastBackward: IconType;
export declare const FaFastForward: IconType;
export declare const FaFaucet: IconType;
export declare const FaFax: IconType;
export declare const FaFeatherAlt: IconType;
export declare const FaFeather: IconType;
export declare const FaFemale: IconType;
export declare const FaFighterJet: IconType;
export declare const FaFileAlt: IconType;
export declare const FaFileArchive: IconType;
export declare const FaFileAudio: IconType;
export declare const FaFileCode: IconType;
export declare const FaFileContract: IconType;
export declare const FaFileCsv: IconType;
export declare const FaFileDownload: IconType;
export declare const FaFileExcel: IconType;
export declare const FaFileExport: IconType;
export declare const FaFileImage: IconType;
export declare const FaFileImport: IconType;
export declare const FaFileInvoiceDollar: IconType;
export declare const FaFileInvoice: IconType;
export declare const FaFileMedicalAlt: IconType;
export declare const FaFileMedical: IconType;
export declare const FaFilePdf: IconType;
export declare const FaFilePowerpoint: IconType;
export declare const FaFilePrescription: IconType;
export declare const FaFileSignature: IconType;
export declare const FaFileUpload: IconType;
export declare const FaFileVideo: IconType;
export declare const FaFileWord: IconType;
export declare const FaFile: IconType;
export declare const FaFillDrip: IconType;
export declare const FaFill: IconType;
export declare const FaFilm: IconType;
export declare const FaFilter: IconType;
export declare const FaFingerprint: IconType;
export declare const FaFireAlt: IconType;
export declare const FaFireExtinguisher: IconType;
export declare const FaFire: IconType;
export declare const FaFirstAid: IconType;
export declare const FaFish: IconType;
export declare const FaFistRaised: IconType;
export declare const FaFlagCheckered: IconType;
export declare const FaFlagUsa: IconType;
export declare const FaFlag: IconType;
export declare const FaFlask: IconType;
export declare const FaFlushed: IconType;
export declare const FaFolderMinus: IconType;
export declare const FaFolderOpen: IconType;
export declare const FaFolderPlus: IconType;
export declare const FaFolder: IconType;
export declare const FaFont: IconType;
export declare const FaFootballBall: IconType;
export declare const FaForward: IconType;
export declare const FaFrog: IconType;
export declare const FaFrownOpen: IconType;
export declare const FaFrown: IconType;
export declare const FaFunnelDollar: IconType;
export declare const FaFutbol: IconType;
export declare const FaGamepad: IconType;
export declare const FaGasPump: IconType;
export declare const FaGavel: IconType;
export declare const FaGem: IconType;
export declare const FaGenderless: IconType;
export declare const FaGhost: IconType;
export declare const FaGift: IconType;
export declare const FaGifts: IconType;
export declare const FaGlassCheers: IconType;
export declare const FaGlassMartiniAlt: IconType;
export declare const FaGlassMartini: IconType;
export declare const FaGlassWhiskey: IconType;
export declare const FaGlasses: IconType;
export declare const FaGlobeAfrica: IconType;
export declare const FaGlobeAmericas: IconType;
export declare const FaGlobeAsia: IconType;
export declare const FaGlobeEurope: IconType;
export declare const FaGlobe: IconType;
export declare const FaGolfBall: IconType;
export declare const FaGopuram: IconType;
export declare const FaGraduationCap: IconType;
export declare const FaGreaterThanEqual: IconType;
export declare const FaGreaterThan: IconType;
export declare const FaGrimace: IconType;
export declare const FaGrinAlt: IconType;
export declare const FaGrinBeamSweat: IconType;
export declare const FaGrinBeam: IconType;
export declare const FaGrinHearts: IconType;
export declare const FaGrinSquintTears: IconType;
export declare const FaGrinSquint: IconType;
export declare const FaGrinStars: IconType;
export declare const FaGrinTears: IconType;
export declare const FaGrinTongueSquint: IconType;
export declare const FaGrinTongueWink: IconType;
export declare const FaGrinTongue: IconType;
export declare const FaGrinWink: IconType;
export declare const FaGrin: IconType;
export declare const FaGripHorizontal: IconType;
export declare const FaGripLinesVertical: IconType;
export declare const FaGripLines: IconType;
export declare const FaGripVertical: IconType;
export declare const FaGuitar: IconType;
export declare const FaHSquare: IconType;
export declare const FaHamburger: IconType;
export declare const FaHammer: IconType;
export declare const FaHamsa: IconType;
export declare const FaHandHoldingHeart: IconType;
export declare const FaHandHoldingMedical: IconType;
export declare const FaHandHoldingUsd: IconType;
export declare const FaHandHoldingWater: IconType;
export declare const FaHandHolding: IconType;
export declare const FaHandLizard: IconType;
export declare const FaHandMiddleFinger: IconType;
export declare const FaHandPaper: IconType;
export declare const FaHandPeace: IconType;
export declare const FaHandPointDown: IconType;
export declare const FaHandPointLeft: IconType;
export declare const FaHandPointRight: IconType;
export declare const FaHandPointUp: IconType;
export declare const FaHandPointer: IconType;
export declare const FaHandRock: IconType;
export declare const FaHandScissors: IconType;
export declare const FaHandSparkles: IconType;
export declare const FaHandSpock: IconType;
export declare const FaHandsHelping: IconType;
export declare const FaHandsWash: IconType;
export declare const FaHands: IconType;
export declare const FaHandshakeAltSlash: IconType;
export declare const FaHandshakeSlash: IconType;
export declare const FaHandshake: IconType;
export declare const FaHanukiah: IconType;
export declare const FaHardHat: IconType;
export declare const FaHashtag: IconType;
export declare const FaHatCowboySide: IconType;
export declare const FaHatCowboy: IconType;
export declare const FaHatWizard: IconType;
export declare const FaHdd: IconType;
export declare const FaHeadSideCoughSlash: IconType;
export declare const FaHeadSideCough: IconType;
export declare const FaHeadSideMask: IconType;
export declare const FaHeadSideVirus: IconType;
export declare const FaHeading: IconType;
export declare const FaHeadphonesAlt: IconType;
export declare const FaHeadphones: IconType;
export declare const FaHeadset: IconType;
export declare const FaHeartBroken: IconType;
export declare const FaHeart: IconType;
export declare const FaHeartbeat: IconType;
export declare const FaHelicopter: IconType;
export declare const FaHighlighter: IconType;
export declare const FaHiking: IconType;
export declare const FaHippo: IconType;
export declare const FaHistory: IconType;
export declare const FaHockeyPuck: IconType;
export declare const FaHollyBerry: IconType;
export declare const FaHome: IconType;
export declare const FaHorseHead: IconType;
export declare const FaHorse: IconType;
export declare const FaHospitalAlt: IconType;
export declare const FaHospitalSymbol: IconType;
export declare const FaHospitalUser: IconType;
export declare const FaHospital: IconType;
export declare const FaHotTub: IconType;
export declare const FaHotdog: IconType;
export declare const FaHotel: IconType;
export declare const FaHourglassEnd: IconType;
export declare const FaHourglassHalf: IconType;
export declare const FaHourglassStart: IconType;
export declare const FaHourglass: IconType;
export declare const FaHouseDamage: IconType;
export declare const FaHouseUser: IconType;
export declare const FaHryvnia: IconType;
export declare const FaICursor: IconType;
export declare const FaIceCream: IconType;
export declare const FaIcicles: IconType;
export declare const FaIcons: IconType;
export declare const FaIdBadge: IconType;
export declare const FaIdCardAlt: IconType;
export declare const FaIdCard: IconType;
export declare const FaIgloo: IconType;
export declare const FaImage: IconType;
export declare const FaImages: IconType;
export declare const FaInbox: IconType;
export declare const FaIndent: IconType;
export declare const FaIndustry: IconType;
export declare const FaInfinity: IconType;
export declare const FaInfoCircle: IconType;
export declare const FaInfo: IconType;
export declare const FaItalic: IconType;
export declare const FaJedi: IconType;
export declare const FaJoint: IconType;
export declare const FaJournalWhills: IconType;
export declare const FaKaaba: IconType;
export declare const FaKey: IconType;
export declare const FaKeyboard: IconType;
export declare const FaKhanda: IconType;
export declare const FaKissBeam: IconType;
export declare const FaKissWinkHeart: IconType;
export declare const FaKiss: IconType;
export declare const FaKiwiBird: IconType;
export declare const FaLandmark: IconType;
export declare const FaLanguage: IconType;
export declare const FaLaptopCode: IconType;
export declare const FaLaptopHouse: IconType;
export declare const FaLaptopMedical: IconType;
export declare const FaLaptop: IconType;
export declare const FaLaughBeam: IconType;
export declare const FaLaughSquint: IconType;
export declare const FaLaughWink: IconType;
export declare const FaLaugh: IconType;
export declare const FaLayerGroup: IconType;
export declare const FaLeaf: IconType;
export declare const FaLemon: IconType;
export declare const FaLessThanEqual: IconType;
export declare const FaLessThan: IconType;
export declare const FaLevelDownAlt: IconType;
export declare const FaLevelUpAlt: IconType;
export declare const FaLifeRing: IconType;
export declare const FaLightbulb: IconType;
export declare const FaLink: IconType;
export declare const FaLiraSign: IconType;
export declare const FaListAlt: IconType;
export declare const FaListOl: IconType;
export declare const FaListUl: IconType;
export declare const FaList: IconType;
export declare const FaLocationArrow: IconType;
export declare const FaLockOpen: IconType;
export declare const FaLock: IconType;
export declare const FaLongArrowAltDown: IconType;
export declare const FaLongArrowAltLeft: IconType;
export declare const FaLongArrowAltRight: IconType;
export declare const FaLongArrowAltUp: IconType;
export declare const FaLowVision: IconType;
export declare const FaLuggageCart: IconType;
export declare const FaLungsVirus: IconType;
export declare const FaLungs: IconType;
export declare const FaMagic: IconType;
export declare const FaMagnet: IconType;
export declare const FaMailBulk: IconType;
export declare const FaMale: IconType;
export declare const FaMapMarkedAlt: IconType;
export declare const FaMapMarked: IconType;
export declare const FaMapMarkerAlt: IconType;
export declare const FaMapMarker: IconType;
export declare const FaMapPin: IconType;
export declare const FaMapSigns: IconType;
export declare const FaMap: IconType;
export declare const FaMarker: IconType;
export declare const FaMarsDouble: IconType;
export declare const FaMarsStrokeH: IconType;
export declare const FaMarsStrokeV: IconType;
export declare const FaMarsStroke: IconType;
export declare const FaMars: IconType;
export declare const FaMask: IconType;
export declare const FaMedal: IconType;
export declare const FaMedkit: IconType;
export declare const FaMehBlank: IconType;
export declare const FaMehRollingEyes: IconType;
export declare const FaMeh: IconType;
export declare const FaMemory: IconType;
export declare const FaMenorah: IconType;
export declare const FaMercury: IconType;
export declare const FaMeteor: IconType;
export declare const FaMicrochip: IconType;
export declare const FaMicrophoneAltSlash: IconType;
export declare const FaMicrophoneAlt: IconType;
export declare const FaMicrophoneSlash: IconType;
export declare const FaMicrophone: IconType;
export declare const FaMicroscope: IconType;
export declare const FaMinusCircle: IconType;
export declare const FaMinusSquare: IconType;
export declare const FaMinus: IconType;
export declare const FaMitten: IconType;
export declare const FaMobileAlt: IconType;
export declare const FaMobile: IconType;
export declare const FaMoneyBillAlt: IconType;
export declare const FaMoneyBillWaveAlt: IconType;
export declare const FaMoneyBillWave: IconType;
export declare const FaMoneyBill: IconType;
export declare const FaMoneyCheckAlt: IconType;
export declare const FaMoneyCheck: IconType;
export declare const FaMonument: IconType;
export declare const FaMoon: IconType;
export declare const FaMortarPestle: IconType;
export declare const FaMosque: IconType;
export declare const FaMotorcycle: IconType;
export declare const FaMountain: IconType;
export declare const FaMousePointer: IconType;
export declare const FaMouse: IconType;
export declare const FaMugHot: IconType;
export declare const FaMusic: IconType;
export declare const FaNetworkWired: IconType;
export declare const FaNeuter: IconType;
export declare const FaNewspaper: IconType;
export declare const FaNotEqual: IconType;
export declare const FaNotesMedical: IconType;
export declare const FaObjectGroup: IconType;
export declare const FaObjectUngroup: IconType;
export declare const FaOilCan: IconType;
export declare const FaOm: IconType;
export declare const FaOtter: IconType;
export declare const FaOutdent: IconType;
export declare const FaPager: IconType;
export declare const FaPaintBrush: IconType;
export declare const FaPaintRoller: IconType;
export declare const FaPalette: IconType;
export declare const FaPallet: IconType;
export declare const FaPaperPlane: IconType;
export declare const FaPaperclip: IconType;
export declare const FaParachuteBox: IconType;
export declare const FaParagraph: IconType;
export declare const FaParking: IconType;
export declare const FaPassport: IconType;
export declare const FaPastafarianism: IconType;
export declare const FaPaste: IconType;
export declare const FaPauseCircle: IconType;
export declare const FaPause: IconType;
export declare const FaPaw: IconType;
export declare const FaPeace: IconType;
export declare const FaPenAlt: IconType;
export declare const FaPenFancy: IconType;
export declare const FaPenNib: IconType;
export declare const FaPenSquare: IconType;
export declare const FaPen: IconType;
export declare const FaPencilAlt: IconType;
export declare const FaPencilRuler: IconType;
export declare const FaPeopleArrows: IconType;
export declare const FaPeopleCarry: IconType;
export declare const FaPepperHot: IconType;
export declare const FaPercent: IconType;
export declare const FaPercentage: IconType;
export declare const FaPersonBooth: IconType;
export declare const FaPhoneAlt: IconType;
export declare const FaPhoneSlash: IconType;
export declare const FaPhoneSquareAlt: IconType;
export declare const FaPhoneSquare: IconType;
export declare const FaPhoneVolume: IconType;
export declare const FaPhone: IconType;
export declare const FaPhotoVideo: IconType;
export declare const FaPiggyBank: IconType;
export declare const FaPills: IconType;
export declare const FaPizzaSlice: IconType;
export declare const FaPlaceOfWorship: IconType;
export declare const FaPlaneArrival: IconType;
export declare const FaPlaneDeparture: IconType;
export declare const FaPlaneSlash: IconType;
export declare const FaPlane: IconType;
export declare const FaPlayCircle: IconType;
export declare const FaPlay: IconType;
export declare const FaPlug: IconType;
export declare const FaPlusCircle: IconType;
export declare const FaPlusSquare: IconType;
export declare const FaPlus: IconType;
export declare const FaPodcast: IconType;
export declare const FaPollH: IconType;
export declare const FaPoll: IconType;
export declare const FaPooStorm: IconType;
export declare const FaPoo: IconType;
export declare const FaPoop: IconType;
export declare const FaPortrait: IconType;
export declare const FaPoundSign: IconType;
export declare const FaPowerOff: IconType;
export declare const FaPray: IconType;
export declare const FaPrayingHands: IconType;
export declare const FaPrescriptionBottleAlt: IconType;
export declare const FaPrescriptionBottle: IconType;
export declare const FaPrescription: IconType;
export declare const FaPrint: IconType;
export declare const FaProcedures: IconType;
export declare const FaProjectDiagram: IconType;
export declare const FaPumpMedical: IconType;
export declare const FaPumpSoap: IconType;
export declare const FaPuzzlePiece: IconType;
export declare const FaQrcode: IconType;
export declare const FaQuestionCircle: IconType;
export declare const FaQuestion: IconType;
export declare const FaQuidditch: IconType;
export declare const FaQuoteLeft: IconType;
export declare const FaQuoteRight: IconType;
export declare const FaQuran: IconType;
export declare const FaRadiationAlt: IconType;
export declare const FaRadiation: IconType;
export declare const FaRainbow: IconType;
export declare const FaRandom: IconType;
export declare const FaReceipt: IconType;
export declare const FaRecordVinyl: IconType;
export declare const FaRecycle: IconType;
export declare const FaRedoAlt: IconType;
export declare const FaRedo: IconType;
export declare const FaRegistered: IconType;
export declare const FaRemoveFormat: IconType;
export declare const FaReplyAll: IconType;
export declare const FaReply: IconType;
export declare const FaRepublican: IconType;
export declare const FaRestroom: IconType;
export declare const FaRetweet: IconType;
export declare const FaRibbon: IconType;
export declare const FaRing: IconType;
export declare const FaRoad: IconType;
export declare const FaRobot: IconType;
export declare const FaRocket: IconType;
export declare const FaRoute: IconType;
export declare const FaRssSquare: IconType;
export declare const FaRss: IconType;
export declare const FaRubleSign: IconType;
export declare const FaRulerCombined: IconType;
export declare const FaRulerHorizontal: IconType;
export declare const FaRulerVertical: IconType;
export declare const FaRuler: IconType;
export declare const FaRunning: IconType;
export declare const FaRupeeSign: IconType;
export declare const FaSadCry: IconType;
export declare const FaSadTear: IconType;
export declare const FaSatelliteDish: IconType;
export declare const FaSatellite: IconType;
export declare const FaSave: IconType;
export declare const FaSchool: IconType;
export declare const FaScrewdriver: IconType;
export declare const FaScroll: IconType;
export declare const FaSdCard: IconType;
export declare const FaSearchDollar: IconType;
export declare const FaSearchLocation: IconType;
export declare const FaSearchMinus: IconType;
export declare const FaSearchPlus: IconType;
export declare const FaSearch: IconType;
export declare const FaSeedling: IconType;
export declare const FaServer: IconType;
export declare const FaShapes: IconType;
export declare const FaShareAltSquare: IconType;
export declare const FaShareAlt: IconType;
export declare const FaShareSquare: IconType;
export declare const FaShare: IconType;
export declare const FaShekelSign: IconType;
export declare const FaShieldAlt: IconType;
export declare const FaShieldVirus: IconType;
export declare const FaShip: IconType;
export declare const FaShippingFast: IconType;
export declare const FaShoePrints: IconType;
export declare const FaShoppingBag: IconType;
export declare const FaShoppingBasket: IconType;
export declare const FaShoppingCart: IconType;
export declare const FaShower: IconType;
export declare const FaShuttleVan: IconType;
export declare const FaSignInAlt: IconType;
export declare const FaSignLanguage: IconType;
export declare const FaSignOutAlt: IconType;
export declare const FaSign: IconType;
export declare const FaSignal: IconType;
export declare const FaSignature: IconType;
export declare const FaSimCard: IconType;
export declare const FaSink: IconType;
export declare const FaSitemap: IconType;
export declare const FaSkating: IconType;
export declare const FaSkiingNordic: IconType;
export declare const FaSkiing: IconType;
export declare const FaSkullCrossbones: IconType;
export declare const FaSkull: IconType;
export declare const FaSlash: IconType;
export declare const FaSleigh: IconType;
export declare const FaSlidersH: IconType;
export declare const FaSmileBeam: IconType;
export declare const FaSmileWink: IconType;
export declare const FaSmile: IconType;
export declare const FaSmog: IconType;
export declare const FaSmokingBan: IconType;
export declare const FaSmoking: IconType;
export declare const FaSms: IconType;
export declare const FaSnowboarding: IconType;
export declare const FaSnowflake: IconType;
export declare const FaSnowman: IconType;
export declare const FaSnowplow: IconType;
export declare const FaSoap: IconType;
export declare const FaSocks: IconType;
export declare const FaSolarPanel: IconType;
export declare const FaSortAlphaDownAlt: IconType;
export declare const FaSortAlphaDown: IconType;
export declare const FaSortAlphaUpAlt: IconType;
export declare const FaSortAlphaUp: IconType;
export declare const FaSortAmountDownAlt: IconType;
export declare const FaSortAmountDown: IconType;
export declare const FaSortAmountUpAlt: IconType;
export declare const FaSortAmountUp: IconType;
export declare const FaSortDown: IconType;
export declare const FaSortNumericDownAlt: IconType;
export declare const FaSortNumericDown: IconType;
export declare const FaSortNumericUpAlt: IconType;
export declare const FaSortNumericUp: IconType;
export declare const FaSortUp: IconType;
export declare const FaSort: IconType;
export declare const FaSpa: IconType;
export declare const FaSpaceShuttle: IconType;
export declare const FaSpellCheck: IconType;
export declare const FaSpider: IconType;
export declare const FaSpinner: IconType;
export declare const FaSplotch: IconType;
export declare const FaSprayCan: IconType;
export declare const FaSquareFull: IconType;
export declare const FaSquareRootAlt: IconType;
export declare const FaSquare: IconType;
export declare const FaStamp: IconType;
export declare const FaStarAndCrescent: IconType;
export declare const FaStarHalfAlt: IconType;
export declare const FaStarHalf: IconType;
export declare const FaStarOfDavid: IconType;
export declare const FaStarOfLife: IconType;
export declare const FaStar: IconType;
export declare const FaStepBackward: IconType;
export declare const FaStepForward: IconType;
export declare const FaStethoscope: IconType;
export declare const FaStickyNote: IconType;
export declare const FaStopCircle: IconType;
export declare const FaStop: IconType;
export declare const FaStopwatch20: IconType;
export declare const FaStopwatch: IconType;
export declare const FaStoreAltSlash: IconType;
export declare const FaStoreAlt: IconType;
export declare const FaStoreSlash: IconType;
export declare const FaStore: IconType;
export declare const FaStream: IconType;
export declare const FaStreetView: IconType;
export declare const FaStrikethrough: IconType;
export declare const FaStroopwafel: IconType;
export declare const FaSubscript: IconType;
export declare const FaSubway: IconType;
export declare const FaSuitcaseRolling: IconType;
export declare const FaSuitcase: IconType;
export declare const FaSun: IconType;
export declare const FaSuperscript: IconType;
export declare const FaSurprise: IconType;
export declare const FaSwatchbook: IconType;
export declare const FaSwimmer: IconType;
export declare const FaSwimmingPool: IconType;
export declare const FaSynagogue: IconType;
export declare const FaSyncAlt: IconType;
export declare const FaSync: IconType;
export declare const FaSyringe: IconType;
export declare const FaTableTennis: IconType;
export declare const FaTable: IconType;
export declare const FaTabletAlt: IconType;
export declare const FaTablet: IconType;
export declare const FaTablets: IconType;
export declare const FaTachometerAlt: IconType;
export declare const FaTag: IconType;
export declare const FaTags: IconType;
export declare const FaTape: IconType;
export declare const FaTasks: IconType;
export declare const FaTaxi: IconType;
export declare const FaTeethOpen: IconType;
export declare const FaTeeth: IconType;
export declare const FaTemperatureHigh: IconType;
export declare const FaTemperatureLow: IconType;
export declare const FaTenge: IconType;
export declare const FaTerminal: IconType;
export declare const FaTextHeight: IconType;
export declare const FaTextWidth: IconType;
export declare const FaThLarge: IconType;
export declare const FaThList: IconType;
export declare const FaTh: IconType;
export declare const FaTheaterMasks: IconType;
export declare const FaThermometerEmpty: IconType;
export declare const FaThermometerFull: IconType;
export declare const FaThermometerHalf: IconType;
export declare const FaThermometerQuarter: IconType;
export declare const FaThermometerThreeQuarters: IconType;
export declare const FaThermometer: IconType;
export declare const FaThumbsDown: IconType;
export declare const FaThumbsUp: IconType;
export declare const FaThumbtack: IconType;
export declare const FaTicketAlt: IconType;
export declare const FaTimesCircle: IconType;
export declare const FaTimes: IconType;
export declare const FaTintSlash: IconType;
export declare const FaTint: IconType;
export declare const FaTired: IconType;
export declare const FaToggleOff: IconType;
export declare const FaToggleOn: IconType;
export declare const FaToiletPaperSlash: IconType;
export declare const FaToiletPaper: IconType;
export declare const FaToilet: IconType;
export declare const FaToolbox: IconType;
export declare const FaTools: IconType;
export declare const FaTooth: IconType;
export declare const FaTorah: IconType;
export declare const FaToriiGate: IconType;
export declare const FaTractor: IconType;
export declare const FaTrademark: IconType;
export declare const FaTrafficLight: IconType;
export declare const FaTrailer: IconType;
export declare const FaTrain: IconType;
export declare const FaTram: IconType;
export declare const FaTransgenderAlt: IconType;
export declare const FaTransgender: IconType;
export declare const FaTrashAlt: IconType;
export declare const FaTrashRestoreAlt: IconType;
export declare const FaTrashRestore: IconType;
export declare const FaTrash: IconType;
export declare const FaTree: IconType;
export declare const FaTrophy: IconType;
export declare const FaTruckLoading: IconType;
export declare const FaTruckMonster: IconType;
export declare const FaTruckMoving: IconType;
export declare const FaTruckPickup: IconType;
export declare const FaTruck: IconType;
export declare const FaTshirt: IconType;
export declare const FaTty: IconType;
export declare const FaTv: IconType;
export declare const FaUmbrellaBeach: IconType;
export declare const FaUmbrella: IconType;
export declare const FaUnderline: IconType;
export declare const FaUndoAlt: IconType;
export declare const FaUndo: IconType;
export declare const FaUniversalAccess: IconType;
export declare const FaUniversity: IconType;
export declare const FaUnlink: IconType;
export declare const FaUnlockAlt: IconType;
export declare const FaUnlock: IconType;
export declare const FaUpload: IconType;
export declare const FaUserAltSlash: IconType;
export declare const FaUserAlt: IconType;
export declare const FaUserAstronaut: IconType;
export declare const FaUserCheck: IconType;
export declare const FaUserCircle: IconType;
export declare const FaUserClock: IconType;
export declare const FaUserCog: IconType;
export declare const FaUserEdit: IconType;
export declare const FaUserFriends: IconType;
export declare const FaUserGraduate: IconType;
export declare const FaUserInjured: IconType;
export declare const FaUserLock: IconType;
export declare const FaUserMd: IconType;
export declare const FaUserMinus: IconType;
export declare const FaUserNinja: IconType;
export declare const FaUserNurse: IconType;
export declare const FaUserPlus: IconType;
export declare const FaUserSecret: IconType;
export declare const FaUserShield: IconType;
export declare const FaUserSlash: IconType;
export declare const FaUserTag: IconType;
export declare const FaUserTie: IconType;
export declare const FaUserTimes: IconType;
export declare const FaUser: IconType;
export declare const FaUsersCog: IconType;
export declare const FaUsersSlash: IconType;
export declare const FaUsers: IconType;
export declare const FaUtensilSpoon: IconType;
export declare const FaUtensils: IconType;
export declare const FaVectorSquare: IconType;
export declare const FaVenusDouble: IconType;
export declare const FaVenusMars: IconType;
export declare const FaVenus: IconType;
export declare const FaVestPatches: IconType;
export declare const FaVest: IconType;
export declare const FaVial: IconType;
export declare const FaVials: IconType;
export declare const FaVideoSlash: IconType;
export declare const FaVideo: IconType;
export declare const FaVihara: IconType;
export declare const FaVirusSlash: IconType;
export declare const FaVirus: IconType;
export declare const FaViruses: IconType;
export declare const FaVoicemail: IconType;
export declare const FaVolleyballBall: IconType;
export declare const FaVolumeDown: IconType;
export declare const FaVolumeMute: IconType;
export declare const FaVolumeOff: IconType;
export declare const FaVolumeUp: IconType;
export declare const FaVoteYea: IconType;
export declare const FaVrCardboard: IconType;
export declare const FaWalking: IconType;
export declare const FaWallet: IconType;
export declare const FaWarehouse: IconType;
export declare const FaWater: IconType;
export declare const FaWaveSquare: IconType;
export declare const FaWeightHanging: IconType;
export declare const FaWeight: IconType;
export declare const FaWheelchair: IconType;
export declare const FaWifi: IconType;
export declare const FaWind: IconType;
export declare const FaWindowClose: IconType;
export declare const FaWindowMaximize: IconType;
export declare const FaWindowMinimize: IconType;
export declare const FaWindowRestore: IconType;
export declare const FaWineBottle: IconType;
export declare const FaWineGlassAlt: IconType;
export declare const FaWineGlass: IconType;
export declare const FaWonSign: IconType;
export declare const FaWrench: IconType;
export declare const FaXRay: IconType;
export declare const FaYenSign: IconType;
export declare const FaYinYang: IconType;
export declare const FaRegAddressBook: IconType;
export declare const FaRegAddressCard: IconType;
export declare const FaRegAngry: IconType;
export declare const FaRegArrowAltCircleDown: IconType;
export declare const FaRegArrowAltCircleLeft: IconType;
export declare const FaRegArrowAltCircleRight: IconType;
export declare const FaRegArrowAltCircleUp: IconType;
export declare const FaRegBellSlash: IconType;
export declare const FaRegBell: IconType;
export declare const FaRegBookmark: IconType;
export declare const FaRegBuilding: IconType;
export declare const FaRegCalendarAlt: IconType;
export declare const FaRegCalendarCheck: IconType;
export declare const FaRegCalendarMinus: IconType;
export declare const FaRegCalendarPlus: IconType;
export declare const FaRegCalendarTimes: IconType;
export declare const FaRegCalendar: IconType;
export declare const FaRegCaretSquareDown: IconType;
export declare const FaRegCaretSquareLeft: IconType;
export declare const FaRegCaretSquareRight: IconType;
export declare const FaRegCaretSquareUp: IconType;
export declare const FaRegChartBar: IconType;
export declare const FaRegCheckCircle: IconType;
export declare const FaRegCheckSquare: IconType;
export declare const FaRegCircle: IconType;
export declare const FaRegClipboard: IconType;
export declare const FaRegClock: IconType;
export declare const FaRegClone: IconType;
export declare const FaRegClosedCaptioning: IconType;
export declare const FaRegCommentAlt: IconType;
export declare const FaRegCommentDots: IconType;
export declare const FaRegComment: IconType;
export declare const FaRegComments: IconType;
export declare const FaRegCompass: IconType;
export declare const FaRegCopy: IconType;
export declare const FaRegCopyright: IconType;
export declare const FaRegCreditCard: IconType;
export declare const FaRegDizzy: IconType;
export declare const FaRegDotCircle: IconType;
export declare const FaRegEdit: IconType;
export declare const FaRegEnvelopeOpen: IconType;
export declare const FaRegEnvelope: IconType;
export declare const FaRegEyeSlash: IconType;
export declare const FaRegEye: IconType;
export declare const FaRegFileAlt: IconType;
export declare const FaRegFileArchive: IconType;
export declare const FaRegFileAudio: IconType;
export declare const FaRegFileCode: IconType;
export declare const FaRegFileExcel: IconType;
export declare const FaRegFileImage: IconType;
export declare const FaRegFilePdf: IconType;
export declare const FaRegFilePowerpoint: IconType;
export declare const FaRegFileVideo: IconType;
export declare const FaRegFileWord: IconType;
export declare const FaRegFile: IconType;
export declare const FaRegFlag: IconType;
export declare const FaRegFlushed: IconType;
export declare const FaRegFolderOpen: IconType;
export declare const FaRegFolder: IconType;
export declare const FaRegFontAwesomeLogoFull: IconType;
export declare const FaRegFrownOpen: IconType;
export declare const FaRegFrown: IconType;
export declare const FaRegFutbol: IconType;
export declare const FaRegGem: IconType;
export declare const FaRegGrimace: IconType;
export declare const FaRegGrinAlt: IconType;
export declare const FaRegGrinBeamSweat: IconType;
export declare const FaRegGrinBeam: IconType;
export declare const FaRegGrinHearts: IconType;
export declare const FaRegGrinSquintTears: IconType;
export declare const FaRegGrinSquint: IconType;
export declare const FaRegGrinStars: IconType;
export declare const FaRegGrinTears: IconType;
export declare const FaRegGrinTongueSquint: IconType;
export declare const FaRegGrinTongueWink: IconType;
export declare const FaRegGrinTongue: IconType;
export declare const FaRegGrinWink: IconType;
export declare const FaRegGrin: IconType;
export declare const FaRegHandLizard: IconType;
export declare const FaRegHandPaper: IconType;
export declare const FaRegHandPeace: IconType;
export declare const FaRegHandPointDown: IconType;
export declare const FaRegHandPointLeft: IconType;
export declare const FaRegHandPointRight: IconType;
export declare const FaRegHandPointUp: IconType;
export declare const FaRegHandPointer: IconType;
export declare const FaRegHandRock: IconType;
export declare const FaRegHandScissors: IconType;
export declare const FaRegHandSpock: IconType;
export declare const FaRegHandshake: IconType;
export declare const FaRegHdd: IconType;
export declare const FaRegHeart: IconType;
export declare const FaRegHospital: IconType;
export declare const FaRegHourglass: IconType;
export declare const FaRegIdBadge: IconType;
export declare const FaRegIdCard: IconType;
export declare const FaRegImage: IconType;
export declare const FaRegImages: IconType;
export declare const FaRegKeyboard: IconType;
export declare const FaRegKissBeam: IconType;
export declare const FaRegKissWinkHeart: IconType;
export declare const FaRegKiss: IconType;
export declare const FaRegLaughBeam: IconType;
export declare const FaRegLaughSquint: IconType;
export declare const FaRegLaughWink: IconType;
export declare const FaRegLaugh: IconType;
export declare const FaRegLemon: IconType;
export declare const FaRegLifeRing: IconType;
export declare const FaRegLightbulb: IconType;
export declare const FaRegListAlt: IconType;
export declare const FaRegMap: IconType;
export declare const FaRegMehBlank: IconType;
export declare const FaRegMehRollingEyes: IconType;
export declare const FaRegMeh: IconType;
export declare const FaRegMinusSquare: IconType;
export declare const FaRegMoneyBillAlt: IconType;
export declare const FaRegMoon: IconType;
export declare const FaRegNewspaper: IconType;
export declare const FaRegObjectGroup: IconType;
export declare const FaRegObjectUngroup: IconType;
export declare const FaRegPaperPlane: IconType;
export declare const FaRegPauseCircle: IconType;
export declare const FaRegPlayCircle: IconType;
export declare const FaRegPlusSquare: IconType;
export declare const FaRegQuestionCircle: IconType;
export declare const FaRegRegistered: IconType;
export declare const FaRegSadCry: IconType;
export declare const FaRegSadTear: IconType;
export declare const FaRegSave: IconType;
export declare const FaRegShareSquare: IconType;
export declare const FaRegSmileBeam: IconType;
export declare const FaRegSmileWink: IconType;
export declare const FaRegSmile: IconType;
export declare const FaRegSnowflake: IconType;
export declare const FaRegSquare: IconType;
export declare const FaRegStarHalf: IconType;
export declare const FaRegStar: IconType;
export declare const FaRegStickyNote: IconType;
export declare const FaRegStopCircle: IconType;
export declare const FaRegSun: IconType;
export declare const FaRegSurprise: IconType;
export declare const FaRegThumbsDown: IconType;
export declare const FaRegThumbsUp: IconType;
export declare const FaRegTimesCircle: IconType;
export declare const FaRegTired: IconType;
export declare const FaRegTrashAlt: IconType;
export declare const FaRegUserCircle: IconType;
export declare const FaRegUser: IconType;
export declare const FaRegWindowClose: IconType;
export declare const FaRegWindowMaximize: IconType;
export declare const FaRegWindowMinimize: IconType;
export declare const FaRegWindowRestore: IconType;
