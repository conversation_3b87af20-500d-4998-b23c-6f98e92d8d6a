import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { FaBook, FaEdit, FaList, FaVideo, FaClipboardList, FaQuestionCircle } from 'react-icons/fa'
import MenuButton from './MenuButton'

const MainMenu: React.FC = () => {
  const navigate = useNavigate()

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'F11') {
        event.preventDefault()
        if (!document.fullscreenElement) {
          document.documentElement.requestFullscreen()
        } else {
          document.exitFullscreen()
        }
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [])

  const menuItems = [
    {
      id: 'students-book',
      title: "Student's Book",
      icon: FaBook,
      path: '/students-book',
      variant: 'primary' as const
    },
    {
      id: 'vocabulary',
      title: 'Vocabulary List',
      icon: FaList,
      path: '/vocabulary',
      variant: 'secondary' as const
    },
    {
      id: 'workbook',
      title: 'Workbook',
      icon: FaEdit,
      path: '/workbook',
      variant: 'primary' as const
    },
    {
      id: 'videos-worksheets',
      title: 'Videos & Worksheets',
      icon: FaVideo,
      path: '/videos-worksheets',
      variant: 'secondary' as const
    },
    {
      id: 'tests',
      title: 'Tests',
      icon: FaClipboardList,
      path: '/tests',
      variant: 'secondary' as const
    }
  ]

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-8 relative">
      {/* Header */}
      <div className="absolute top-8 left-8 flex items-center">
        <h1 className="text-white text-4xl font-bold tracking-wider">
          PIONEER
        </h1>
        <span className="text-white text-xl ml-4 font-light">
          pre-intermediate
        </span>
      </div>

      {/* Help Button */}
      <div className="absolute top-8 right-8">
        <div className="bg-white bg-opacity-20 rounded-lg p-4 text-white text-sm max-w-xs">
          <div className="flex items-center mb-2">
            <FaQuestionCircle className="mr-2" />
            <span className="font-semibold">Help</span>
          </div>
          <p>Press F11 to enter/exit</p>
          <p>the full screen mode</p>
          <p>and Ctrl+/Ctrl- to</p>
          <p>zoom in/zoom out.</p>
        </div>
      </div>

      {/* Main Menu Grid */}
      <div className="grid grid-cols-2 gap-8 max-w-4xl w-full">
        {menuItems.map((item) => (
          <MenuButton
            key={item.id}
            title={item.title}
            icon={item.icon}
            onClick={() => navigate(item.path)}
            variant={item.variant}
          />
        ))}
      </div>

      {/* Modules & More */}
      <div className="absolute bottom-8 right-8 flex items-center text-white">
        <span className="mr-2">Modules & more</span>
        <div className="w-6 h-6 border border-white rounded flex items-center justify-center">
          <span className="text-xs">▲</span>
        </div>
      </div>

      {/* Version */}
      <div className="absolute bottom-4 right-4 text-white text-xs opacity-70">
        v1.0
      </div>
    </div>
  )
}

export default MainMenu
