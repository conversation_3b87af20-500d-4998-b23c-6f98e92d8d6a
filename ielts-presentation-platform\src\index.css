@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #1e40af 0%, #059669 100%);
    min-height: 100vh;
    overflow-x: hidden;
  }
}

@layer components {
  .pioneer-button {
    @apply bg-white bg-opacity-90 hover:bg-opacity-100 text-gray-700 font-semibold py-4 px-8 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer;
  }
  
  .pioneer-button-primary {
    @apply bg-purple-400 bg-opacity-80 hover:bg-opacity-90 text-white;
  }
  
  .pioneer-button-secondary {
    @apply bg-white bg-opacity-80 hover:bg-opacity-90 text-gray-700;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
