import React from 'react'
import { IconType } from 'react-icons'

interface MenuButtonProps {
  title: string
  icon: IconType
  onClick: () => void
  variant: 'primary' | 'secondary'
}

const MenuButton: React.FC<MenuButtonProps> = ({ title, icon: Icon, onClick, variant }) => {
  const baseClasses = "flex items-center justify-center p-8 rounded-full text-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer min-h-[120px] min-w-[300px]"
  
  const variantClasses = {
    primary: "bg-purple-400 bg-opacity-80 hover:bg-opacity-90 text-white",
    secondary: "bg-white bg-opacity-80 hover:bg-opacity-90 text-gray-700"
  }

  return (
    <div
      className={`${baseClasses} ${variantClasses[variant]}`}
      onClick={onClick}
    >
      <div className="flex items-center space-x-4">
        <Icon className="text-2xl" />
        <span>{title}</span>
      </div>
    </div>
  )
}

export default MenuButton
