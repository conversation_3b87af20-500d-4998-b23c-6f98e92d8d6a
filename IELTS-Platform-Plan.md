# IELTS Teaching Platform - Project Plan

## 1. Project Overview

### Goals
Create a presentation-style IELTS teaching platform inspired by the Pioneer series desktop program for classroom instruction. The platform will serve as a digital presentation tool for English tutors to deliver IELTS lessons effectively in a classroom environment.

### Scope
- **Primary Focus**: Presentation-only interface (not interactive student platform)
- **Target Audience**: English tutors teaching IELTS preparation courses
- **Use Case**: Classroom projection and lesson delivery
- **Platform**: Web-based application with potential desktop packaging

### Key Objectives
- Replicate the clean, professional interface of Pioneer series
- Provide large, readable content optimized for classroom projection
- Enable smooth navigation between lessons and activities
- Support multimedia content (audio, images, text)
- Offer presentation controls (fullscreen, zoom, navigation)

## 2. Detailed Milestones

### Week 1: Foundation & Core UI Framework
**Objectives:**
- Set up project structure and development environment
- Create main menu interface matching Pioneer design
- Implement basic navigation system
- Establish responsive design foundation

**Deliverables:**
- Project setup with React + TypeScript + Tailwind CSS
- Main menu with 5 primary navigation buttons
- Basic routing system
- Responsive layout for different screen sizes
- Initial styling matching Pioneer color scheme and typography

**Success Criteria:**
- ✅ Main menu displays correctly with Pioneer-style buttons
- ✅ Navigation between sections works smoothly
- ✅ Interface is responsive and projection-ready
- ✅ Code structure is clean and maintainable

### Week 2: Core Components & Content Structure
**Objectives:**
- Develop lesson viewer component
- Create audio player with presentation controls
- Implement content display system
- Build navigation toolbar

**Deliverables:**
- Lesson viewer with header, content area, and sidebar
- Audio player with play/pause, progress bar, and time display
- Content structure for lessons, dialogues, and exercises
- Bottom toolbar with presentation controls
- Sample lesson content integration

**Success Criteria:**
- ✅ Lesson content displays clearly and professionally
- ✅ Audio player functions correctly with visible controls
- ✅ Navigation toolbar provides easy access to features
- ✅ Content is optimized for classroom projection

### Week 3: Content Integration & Multimedia Support
**Objectives:**
- Integrate lesson content and structure
- Implement image and media support
- Create vocabulary and exercise displays
- Add grammar focus boxes and activity sections

**Deliverables:**
- Complete lesson content structure (Student's Book, Workbook)
- Image integration for contextual support
- Vocabulary list display with definitions
- Grammar boxes with examples and exercises
- Speaking & Writing activity sections
- Test and assessment content structure

**Success Criteria:**
- ✅ All content types display correctly
- ✅ Images load and scale appropriately
- ✅ Text formatting is clear and readable
- ✅ Content organization matches IELTS structure

### Week 4: Presentation Features & Polish
**Objectives:**
- Implement fullscreen and zoom functionality
- Add keyboard shortcuts for presentation control
- Create interface hiding/showing features
- Polish design and add final touches

**Deliverables:**
- Fullscreen mode (F11 toggle)
- Zoom controls (Ctrl+/Ctrl- shortcuts)
- Interface hiding for clean content display
- Keyboard navigation shortcuts
- Help system with shortcut reference
- Final design polish and optimization

**Success Criteria:**
- ✅ All presentation controls work seamlessly
- ✅ Keyboard shortcuts enhance teaching flow
- ✅ Interface can be hidden for distraction-free content
- ✅ Platform is ready for classroom use

## 3. Technical Specifications

### Technology Stack
- **Frontend Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS for utility-first styling
- **Build Tool**: Vite for fast development and building
- **Audio Handling**: HTML5 Audio API
- **Icons**: React Icons library
- **Routing**: React Router DOM
- **State Management**: React Context API (for simple state)
- **Package Manager**: npm

### File Structure
```
ielts-presentation-platform/
├── public/
│   ├── index.html
│   ├── favicon.ico
│   └── assets/
│       ├── images/
│       │   ├── lessons/
│       │   └── ui/
│       ├── audio/
│       │   ├── dialogues/
│       │   └── exercises/
│       └── icons/
├── src/
│   ├── components/
│   │   ├── MainMenu/
│   │   │   ├── MainMenu.tsx
│   │   │   └── MenuButton.tsx
│   │   ├── LessonViewer/
│   │   │   ├── LessonViewer.tsx
│   │   │   ├── ContentArea.tsx
│   │   │   └── Sidebar.tsx
│   │   ├── AudioPlayer/
│   │   │   ├── AudioPlayer.tsx
│   │   │   └── AudioControls.tsx
│   │   ├── Navigation/
│   │   │   ├── Toolbar.tsx
│   │   │   └── Breadcrumb.tsx
│   │   └── Common/
│   │       ├── Header.tsx
│   │       └── HelpModal.tsx
│   ├── data/
│   │   ├── lessons/
│   │   │   ├── module1/
│   │   │   └── module2/
│   │   ├── vocabulary/
│   │   ├── tests/
│   │   └── content-structure.ts
│   ├── hooks/
│   │   ├── useAudio.ts
│   │   └── useKeyboard.ts
│   ├── styles/
│   │   ├── globals.css
│   │   └── components.css
│   ├── types/
│   │   └── content.ts
│   ├── utils/
│   │   ├── navigation.ts
│   │   └── content-loader.ts
│   ├── App.tsx
│   └── main.tsx
├── package.json
├── tsconfig.json
├── tailwind.config.js
├── vite.config.ts
└── README.md
```

### Key Components Architecture
- **MainMenu**: Central navigation hub with large buttons
- **LessonViewer**: Main content display with sidebar navigation
- **AudioPlayer**: Multimedia controls for listening exercises
- **Toolbar**: Bottom navigation with presentation controls
- **ContentArea**: Flexible content renderer for different lesson types

## 4. Feature Requirements

### Main Menu Features
- Large, clickable navigation buttons (Student's Book, Workbook, Vocabulary List, Videos & Worksheets, Tests)
- IELTS branding with level indicator
- Help button with keyboard shortcuts
- Expandable "Modules & more" section
- Professional color scheme (blue/green gradient background)

### Lesson Viewer Features
- Clean header with course title, module, and lesson information
- Large content area optimized for projection
- Side navigation for quick access to related materials
- Breadcrumb navigation for context
- Smooth transitions between content

### Audio Player Features
- Large, visible play/pause button
- Progress bar with time display (00:00 format)
- Volume control
- Audio scrubbing capability
- Visual feedback for audio state

### Navigation Features
- Previous/next arrows for content navigation
- Quick jump between modules and lessons
- Keyboard shortcuts (arrow keys, space bar, etc.)
- Home button for returning to main menu

### Presentation Controls
- Fullscreen toggle (F11)
- Zoom in/out (Ctrl+/Ctrl-)
- Interface hiding for clean content display
- Text size adjustment
- Pause/resume functionality

### Content Display Features
- Grammar focus boxes with clear formatting
- Speaking & Writing activity sections
- Image integration with proper scaling
- Dialogue display with character names
- Exercise numbering and clear structure
- Vocabulary definitions and examples

## 5. Implementation Timeline

### Week 1 (Days 1-7)
- **Day 1-2**: Project setup, environment configuration
- **Day 3-4**: Main menu component development
- **Day 5-6**: Basic routing and navigation implementation
- **Day 7**: Responsive design and initial styling

### Week 2 (Days 8-14)
- **Day 8-9**: Lesson viewer component structure
- **Day 10-11**: Audio player implementation
- **Day 12-13**: Content display system
- **Day 14**: Navigation toolbar and controls

### Week 3 (Days 15-21)
- **Day 15-16**: Lesson content integration
- **Day 17-18**: Multimedia support (images, audio files)
- **Day 19-20**: Vocabulary and exercise displays
- **Day 21**: Grammar boxes and activity sections

### Week 4 (Days 22-28)
- **Day 22-23**: Fullscreen and zoom functionality
- **Day 24-25**: Keyboard shortcuts implementation
- **Day 26-27**: Interface hiding and presentation features
- **Day 28**: Final polish, testing, and documentation

## 6. Dependencies and Prerequisites

### Development Environment
- Node.js (v18 or higher)
- npm or yarn package manager
- Modern web browser for testing
- Code editor (VS Code recommended)

### External Resources
- IELTS lesson content (text, audio files, images)
- Pioneer series design reference materials
- Audio files in web-compatible formats (MP3, WAV)
- High-resolution images for lessons

### Setup Requirements
- Git for version control
- Local development server capability
- Screen resolution testing for projection compatibility

### Content Dependencies
- Structured lesson content in JSON or markdown format
- Audio files organized by module and lesson
- Images optimized for web display
- Vocabulary lists with definitions

---

**Project Start Date**: [To be determined]
**Estimated Completion**: 4 weeks from start date
**Project Lead**: AI Assistant
**Stakeholder**: English Tutor (IELTS Instructor)
