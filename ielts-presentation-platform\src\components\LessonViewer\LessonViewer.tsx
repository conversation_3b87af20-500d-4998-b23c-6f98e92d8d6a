import React from 'react'
import { useNavigate } from 'react-router-dom'
import { FaHome, FaArrowLeft, FaArrowRight, FaPlay, FaPause, FaEye, FaCalendar, FaFont, FaComments, FaPaperclip, FaCog } from 'react-icons/fa'

interface LessonViewerProps {
  section: string
}

const LessonViewer: React.FC<LessonViewerProps> = ({ section }) => {
  const navigate = useNavigate()

  const getSectionTitle = (section: string) => {
    switch (section) {
      case 'students-book':
        return "Student's Book"
      case 'workbook':
        return 'Workbook'
      case 'vocabulary':
        return 'Vocabulary List'
      case 'videos-worksheets':
        return 'Videos & Worksheets'
      case 'tests':
        return 'Tests'
      default:
        return 'IELTS Content'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-pioneer-blue to-pioneer-green">
      {/* Header */}
      <div className="bg-pioneer-dark-blue text-white p-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold">PIONEER</h1>
          <span className="text-lg">pre-intermediate</span>
          <span className="text-lg font-semibold">{getSectionTitle(section)}</span>
          <span className="text-lg">/ Module 1</span>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Sidebar */}
        <div className="w-20 bg-pioneer-dark-blue text-white flex flex-col items-center py-4 space-y-6">
          <div className="bg-green-600 p-3 rounded">
            <span className="text-sm font-bold">1a</span>
          </div>
          <div className="text-center">
            <FaHome className="text-xl mb-1" />
            <span className="text-xs">Workbook</span>
          </div>
          <div className="text-center">
            <FaFont className="text-xl mb-1" />
            <span className="text-xs">Vocabulary List</span>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 bg-white p-8 overflow-auto">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">
              Welcome to {getSectionTitle(section)}
            </h2>
            
            <div className="bg-gray-50 rounded-lg p-6 mb-6">
              <h3 className="text-xl font-semibold mb-4">Sample Content</h3>
              <p className="text-gray-700 mb-4">
                This is a placeholder for the {section} content. In a real implementation, 
                this would contain:
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>Interactive lessons and exercises</li>
                <li>Audio content with playback controls</li>
                <li>Images and multimedia materials</li>
                <li>Grammar explanations and examples</li>
                <li>Speaking and writing activities</li>
              </ul>
            </div>

            {/* Sample Audio Player */}
            <div className="bg-white border rounded-lg p-4 mb-6 shadow-sm">
              <div className="flex items-center justify-center space-x-4">
                <FaPlay className="text-2xl text-pioneer-blue cursor-pointer hover:text-pioneer-light-blue" />
                <span className="text-lg">00:00</span>
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div className="bg-pioneer-blue h-2 rounded-full w-0"></div>
                </div>
                <FaPause className="text-2xl text-gray-400" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Toolbar */}
      <div className="bg-pioneer-dark-blue text-white p-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <FaCog className="text-xl cursor-pointer hover:text-gray-300" />
          <div className="bg-white bg-opacity-20 p-2 rounded">
            <FaHome className="text-xl cursor-pointer" onClick={() => navigate('/')} />
          </div>
          <FaHome className="text-xl cursor-pointer hover:text-gray-300" />
          <FaPause className="text-xl cursor-pointer hover:text-gray-300" />
          <FaEye className="text-xl cursor-pointer hover:text-gray-300" />
          <FaCalendar className="text-xl cursor-pointer hover:text-gray-300" />
        </div>

        <div className="flex items-center space-x-4">
          <FaArrowLeft className="text-2xl cursor-pointer hover:text-gray-300" />
          <FaArrowRight className="text-2xl cursor-pointer hover:text-gray-300" />
        </div>

        <div className="flex items-center space-x-4">
          <FaFont className="text-xl cursor-pointer hover:text-gray-300" />
          <FaComments className="text-xl cursor-pointer hover:text-gray-300" />
          <FaPaperclip className="text-xl cursor-pointer hover:text-gray-300" />
          <div className="text-red-500 font-bold">[1]</div>
          <FaCog className="text-xl cursor-pointer hover:text-gray-300" />
        </div>
      </div>
    </div>
  )
}

export default LessonViewer
